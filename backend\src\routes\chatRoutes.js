const express = require('express');
const {
  getUserChats,
  getChatById,
  createChat,
  addMessage,
  markMessagesAsRead
} = require('../controllers/chatController');
const { protect } = require('../middleware/auth');
const { commonValidations, handleValidationErrors } = require('../middleware/validation');
const { chatLimiter } = require('../middleware/security');
const { body } = require('express-validator');

const router = express.Router();

// All chat routes require authentication
router.use(protect);

// GET /api/chat - Get all chats for the current user
router.get('/', [commonValidations.page, commonValidations.limit], getUserChats);

// GET /api/chat/:id - Get chat by ID
router.get('/:id', [commonValidations.mongoId, handleValidationErrors], getChatById);

// POST /api/chat - Create a new chat
router.post('/', [
  body('participantId')
    .notEmpty()
    .isMongoId()
    .withMessage('Valid participant ID is required'),
  handleValidationErrors
], createChat);

// POST /api/chat/:id/message - Add message to chat
router.post('/:id/message', chatLimiter, [
  commonValidations.mongoId,
  body('content')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 1, max: 2000 })
    .escape()
    .withMessage('Message content must be 1-2000 characters'),
  handleValidationErrors
], addMessage);

// PUT /api/chat/:id/read - Mark messages as read
router.put('/:id/read', [commonValidations.mongoId, handleValidationErrors], markMessagesAsRead);

module.exports = router;
