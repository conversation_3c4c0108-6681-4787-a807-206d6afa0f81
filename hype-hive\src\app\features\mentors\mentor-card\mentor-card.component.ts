import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

import { User, InstructionCategory } from '../../../core/models/user.model';

@Component({
  selector: 'app-mentor-card',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatChipsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './mentor-card.component.html',
  styleUrls: ['./mentor-card.component.scss']
})
export class MentorCardComponent {
  @Input() mentor!: User;
  
  /**
   * Get proficiencies for a specific category
   */
  getProficienciesForCategory(category: InstructionCategory): string[] {
    if (!this.mentor || !this.mentor.proficiencies) return [];
    return this.mentor.proficiencies
      .filter(p => p.category === category && p.isSelected)
      .map(p => p.name);
  }
  
  /**
   * Get all selected proficiency categories
   */
  getSelectedCategories(): InstructionCategory[] {
    if (!this.mentor || !this.mentor.proficiencies) return [];
    
    const categories = new Set<InstructionCategory>();
    this.mentor.proficiencies.forEach(p => {
      if (p.isSelected) {
        categories.add(p.category as InstructionCategory);
      }
    });
    
    return Array.from(categories);
  }
  
  /**
   * Get total number of proficiencies
   */
  getTotalProficiencies(): number {
    if (!this.mentor || !this.mentor.proficiencies) return 0;
    return this.mentor.proficiencies.filter(p => p.isSelected).length;
  }
  
  /**
   * Truncate text to a specific length
   */
  truncateText(text: string, maxLength: number = 100): string {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }
}
