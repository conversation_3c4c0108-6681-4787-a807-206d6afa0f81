.chat-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 800px;
  margin: 0 auto;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 10;
  
  button {
    margin-right: 0.5rem;
  }
  
  .chat-info {
    display: flex;
    align-items: center;
    flex: 1;
    
    .avatar {
      margin-right: 1rem;
      
      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    
    .name-status {
      display: flex;
      flex-direction: column;
      
      h2 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 500;
      }
      
      .connection-status {
        display: flex;
        align-items: center;
        font-size: 0.75rem;
        color: #f44336;
        
        mat-icon {
          font-size: 1rem;
          height: 1rem;
          width: 1rem;
          margin-right: 0.25rem;
        }
      }
    }
  }
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 2rem;
  text-align: center;
  
  p {
    margin-top: 1rem;
    color: #666;
  }
  
  button {
    margin-top: 1rem;
    
    &:not(:last-child) {
      margin-right: 0.5rem;
    }
  }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f9f9f9;
  
  .empty-chat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #888;
    
    mat-icon {
      font-size: 3rem;
      height: 3rem;
      width: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }
    
    p {
      margin: 0.5rem 0;
      
      &.hint {
        font-size: 0.875rem;
        opacity: 0.7;
      }
    }
  }
  
  .message-list {
    display: flex;
    flex-direction: column;
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #666;
  
  .typing-dots {
    display: flex;
    margin-right: 0.5rem;
    
    span {
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #888;
      margin-right: 3px;
      animation: typing-dot 1.4s infinite ease-in-out;
      
      &:nth-child(1) {
        animation-delay: 0s;
      }
      
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }
}

@keyframes typing-dot {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

.message-input-container {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem 1rem;
  background-color: #fff;
  border-top: 1px solid #eee;
  
  .message-input {
    flex: 1;
    margin-right: 0.5rem;
  }
  
  .send-button {
    flex-shrink: 0;
  }
}

::ng-deep .message-input {
  .mat-mdc-form-field-subscript-wrapper {
    display: none;
  }
  
  .mat-mdc-form-field-infix {
    padding: 0.5rem 0;
  }
}
