import { InstructionCategory } from './user.model';

export interface Content {
  id: string;
  title: string;
  description: string;
  category: InstructionCategory;
  videoUrl?: string;
  embedUrl?: string;
  thumbnailUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  isPublished: boolean;
}

export interface ContentFilter {
  category?: InstructionCategory;
  searchTerm?: string;
}
