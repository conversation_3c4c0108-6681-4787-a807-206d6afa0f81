<mat-card class="mentor-card">
  <mat-card-header>
    <div mat-card-avatar class="mentor-avatar" [style.background-image]="mentor.discordAvatar ? 'url(' + mentor.discordAvatar + ')' : ''">
      @if (!mentor.discordAvatar) {
        <mat-icon>person</mat-icon>
      }
    </div>
    <mat-card-title>{{ mentor.discordUsername }}</mat-card-title>
    <mat-card-subtitle>
      <span class="mentor-badge">Mentor</span>
    </mat-card-subtitle>
  </mat-card-header>
  
  <mat-card-content>
    @if (mentor.bio) {
      <p class="mentor-bio">{{ truncateText(mentor.bio, 150) }}</p>
    } @else {
      <p class="mentor-bio no-bio">No bio provided</p>
    }
    
    <div class="mentor-categories">
      @for (category of getSelectedCategories(); track category) {
        <mat-chip color="primary" selected>{{ category }}</mat-chip>
      }
    </div>
    
    @if (mentor.socialLinks) {
      <div class="social-links">
        @if (mentor.socialLinks.twitch) {
          <a [href]="mentor.socialLinks.twitch" target="_blank" mat-icon-button matTooltip="Twitch">
            <mat-icon>videogame_asset</mat-icon>
          </a>
        }
        @if (mentor.socialLinks.twitter) {
          <a [href]="mentor.socialLinks.twitter" target="_blank" mat-icon-button matTooltip="Twitter">
            <mat-icon>chat</mat-icon>
          </a>
        }
        @if (mentor.socialLinks.youtube) {
          <a [href]="mentor.socialLinks.youtube" target="_blank" mat-icon-button matTooltip="YouTube">
            <mat-icon>video_library</mat-icon>
          </a>
        }
        @if (mentor.socialLinks.instagram) {
          <a [href]="mentor.socialLinks.instagram" target="_blank" mat-icon-button matTooltip="Instagram">
            <mat-icon>photo_camera</mat-icon>
          </a>
        }
      </div>
    }
  </mat-card-content>
  
  <mat-card-actions>
    <button mat-raised-button color="primary" [routerLink]="['/mentors', mentor._id]">
      View Profile
    </button>
    <button mat-stroked-button color="accent">
      <mat-icon>message</mat-icon> Contact
    </button>
  </mat-card-actions>
</mat-card>
