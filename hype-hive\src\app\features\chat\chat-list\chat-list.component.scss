.chat-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.chat-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  
  h1 {
    margin: 0;
    font-size: 1.5rem;
  }
}

.connection-status {
  display: flex;
  align-items: center;
  background-color: #fff3cd;
  color: #856404;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  
  mat-icon {
    margin-right: 0.5rem;
  }
}

.chat-list-content {
  flex: 1;
  overflow-y: auto;
}

.loading-container, .error-container, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  
  p {
    margin-top: 1rem;
    color: #666;
  }
  
  button {
    margin-top: 1rem;
  }
  
  mat-icon {
    font-size: 3rem;
    height: 3rem;
    width: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
  }
}

.empty-state {
  mat-icon {
    color: #ccc;
  }
  
  h3 {
    margin: 0.5rem 0;
    color: #333;
  }
}

.chat-item {
  display: flex;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  
  &:hover {
    background-color: #f9f9f9;
  }
}

.chat-avatar {
  margin-right: 1rem;
  
  .avatar-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.chat-info {
  flex: 1;
  min-width: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  
  .chat-name {
    margin: 0;
    font-weight: 500;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .chat-time {
    font-size: 0.75rem;
    color: #888;
    white-space: nowrap;
    margin-left: 0.5rem;
  }
}

.chat-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .message-preview {
    margin: 0;
    font-size: 0.875rem;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &.unread {
      font-weight: 500;
      color: #333;
    }
  }
  
  .unread-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    background-color: #3f51b5;
    color: white;
    font-size: 0.75rem;
    margin-left: 0.5rem;
    padding: 0 6px;
  }
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 1rem 0;
}
