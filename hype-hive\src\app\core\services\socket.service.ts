import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { AuthService } from './auth.service';
import { environment } from '../../../environments/environment';
import { MonitoringService } from './monitoring.service';
import { io } from 'socket.io-client';

@Injectable({
  providedIn: 'root'
})
export class SocketService implements OnDestroy {
  private socket: any;
  private connectionStatus = new BehaviorSubject<boolean>(false);
  private reconnecting = new BehaviorSubject<boolean>(false);

  constructor(
    private authService: AuthService,
    private monitoringService: MonitoringService
  ) {}

  /**
   * Initialize socket connection
   */
  public initializeSocket(): void {
    if (!this.authService.isAuthenticated()) {
      console.warn('Cannot initialize socket: User not authenticated');
      return;
    }

    try {
      // Get auth token
      const token = this.authService.getToken();

      // Initialize socket with auth token
      this.socket = io(environment.socketUrl || environment.apiUrl.replace('/api', ''), {
        auth: {
          token
        },
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000
      });

      // Set up event listeners
      this.setupEventListeners();

      // Track connection in monitoring
      this.monitoringService.trackEvent('socket_initialized');
    } catch (error) {
      console.error('Socket initialization error:', error);
      this.monitoringService.trackError('socket_init_error', error);
    }
  }

  /**
   * Set up socket event listeners
   */
  private setupEventListeners(): void {
    // Connection events
    this.socket.on('connect', () => {
      console.log('Socket connected');
      this.connectionStatus.next(true);
      this.reconnecting.next(false);
      this.monitoringService.trackEvent('socket_connected');
    });

    this.socket.on('disconnect', (reason: string) => {
      console.log(`Socket disconnected: ${reason}`);
      this.connectionStatus.next(false);
      this.monitoringService.trackEvent('socket_disconnected', { reason });
    });

    this.socket.on('reconnect_attempt', (attempt: number) => {
      console.log(`Socket reconnection attempt: ${attempt}`);
      this.reconnecting.next(true);
      this.monitoringService.trackEvent('socket_reconnect_attempt', { attempt });
    });

    this.socket.on('reconnect', (attempt: number) => {
      console.log(`Socket reconnected after ${attempt} attempts`);
      this.connectionStatus.next(true);
      this.reconnecting.next(false);
      this.monitoringService.trackEvent('socket_reconnected', { attempt });
    });

    this.socket.on('reconnect_failed', () => {
      console.error('Socket reconnection failed');
      this.reconnecting.next(false);
      this.monitoringService.trackEvent('socket_reconnect_failed');
    });

    this.socket.on('error', (error: any) => {
      console.error('Socket error:', error);
      this.monitoringService.trackError('socket_error', error);
    });
  }

  /**
   * Join a chat room
   * @param chatId Chat ID to join
   */
  public joinChat(chatId: string): void {
    if (!this.socket || !this.connectionStatus.value) {
      console.warn('Cannot join chat: Socket not connected');
      return;
    }

    this.socket.emit('join_chat', chatId);
  }

  /**
   * Leave a chat room
   * @param chatId Chat ID to leave
   */
  public leaveChat(chatId: string): void {
    if (!this.socket || !this.connectionStatus.value) {
      console.warn('Cannot leave chat: Socket not connected');
      return;
    }

    this.socket.emit('leave_chat', chatId);
  }

  /**
   * Send a message to a chat
   * @param chatId Chat ID
   * @param content Message content
   */
  public sendMessage(chatId: string, content: string): void {
    if (!this.socket || !this.connectionStatus.value) {
      console.warn('Cannot send message: Socket not connected');
      return;
    }

    this.socket.emit('send_message', { chatId, content });
  }

  /**
   * Send typing indicator
   * @param chatId Chat ID
   */
  public sendTyping(chatId: string): void {
    if (!this.socket || !this.connectionStatus.value) {
      return;
    }

    this.socket.emit('typing', chatId);
  }

  /**
   * Send stop typing indicator
   * @param chatId Chat ID
   */
  public sendStopTyping(chatId: string): void {
    if (!this.socket || !this.connectionStatus.value) {
      return;
    }

    this.socket.emit('stop_typing', chatId);
  }

  /**
   * Listen for new messages
   * @returns Observable of new messages
   */
  public onNewMessage(): Observable<any> {
    const subject = new Subject<any>();

    if (!this.socket) {
      return subject.asObservable();
    }

    this.socket.on('new_message', (data: any) => {
      subject.next(data);
    });

    return subject.asObservable();
  }

  /**
   * Listen for typing indicators
   * @returns Observable of typing events
   */
  public onTyping(): Observable<any> {
    const subject = new Subject<any>();

    if (!this.socket) {
      return subject.asObservable();
    }

    this.socket.on('typing', (data: any) => {
      subject.next(data);
    });

    return subject.asObservable();
  }

  /**
   * Listen for stop typing indicators
   * @returns Observable of stop typing events
   */
  public onStopTyping(): Observable<any> {
    const subject = new Subject<any>();

    if (!this.socket) {
      return subject.asObservable();
    }

    this.socket.on('stop_typing', (data: any) => {
      subject.next(data);
    });

    return subject.asObservable();
  }

  /**
   * Listen for new notifications
   * @returns Observable of new notifications
   */
  public onNewNotification(): Observable<any> {
    const subject = new Subject<any>();

    if (!this.socket) {
      return subject.asObservable();
    }

    this.socket.on('new_notification', (data: any) => {
      subject.next(data);
    });

    return subject.asObservable();
  }

  /**
   * Get connection status
   * @returns Observable of connection status
   */
  public getConnectionStatus(): Observable<boolean> {
    return this.connectionStatus.asObservable();
  }

  /**
   * Get reconnecting status
   * @returns Observable of reconnecting status
   */
  public getReconnectingStatus(): Observable<boolean> {
    return this.reconnecting.asObservable();
  }

  /**
   * Disconnect socket
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
    }
  }

  /**
   * Clean up on destroy
   */
  ngOnDestroy(): void {
    this.disconnect();
  }
}
