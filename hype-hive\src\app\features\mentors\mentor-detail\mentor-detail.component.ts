import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { ProfileService } from '../../../core/services/profile.service';
import { ContentService } from '../../../core/services/content.service';
import { User, InstructionCategory } from '../../../core/models/user.model';
import { Content } from '../../../core/services/content.service';

@Component({
  selector: 'app-mentor-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatChipsModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './mentor-detail.component.html',
  styleUrls: ['./mentor-detail.component.scss']
})
export class MentorDetailComponent implements OnInit {
  mentor: User | null = null;
  mentorContent: Content[] = [];
  isLoading = true;
  isLoadingContent = false;
  errorMessage = '';
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private profileService: ProfileService,
    private contentService: ContentService
  ) {}
  
  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const mentorId = params.get('id');
      if (mentorId) {
        this.loadMentorProfile(mentorId);
      } else {
        this.errorMessage = 'Mentor ID not provided';
        this.isLoading = false;
      }
    });
  }
  
  /**
   * Load mentor profile
   */
  loadMentorProfile(mentorId: string): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.profileService.getUserProfile(mentorId).subscribe({
      next: (mentor) => {
        this.mentor = mentor;
        this.isLoading = false;
        
        // Load mentor's content
        this.loadMentorContent(mentorId);
      },
      error: (error) => {
        this.errorMessage = 'Failed to load mentor profile. Please try again.';
        this.isLoading = false;
        console.error('Error loading mentor profile:', error);
      }
    });
  }
  
  /**
   * Load mentor's content
   */
  loadMentorContent(mentorId: string): void {
    this.isLoadingContent = true;
    
    this.contentService.getAllContent({ createdBy: mentorId, isPublished: true }).subscribe({
      next: (result) => {
        this.mentorContent = result.content;
        this.isLoadingContent = false;
      },
      error: (error) => {
        console.error('Error loading mentor content:', error);
        this.isLoadingContent = false;
      }
    });
  }
  
  /**
   * Get proficiencies for a specific category
   */
  getProficienciesForCategory(category: InstructionCategory): string[] {
    if (!this.mentor || !this.mentor.proficiencies) return [];
    return this.mentor.proficiencies
      .filter(p => p.category === category && p.isSelected)
      .map(p => p.name);
  }
  
  /**
   * Get all selected proficiency categories
   */
  getSelectedCategories(): InstructionCategory[] {
    if (!this.mentor || !this.mentor.proficiencies) return [];
    
    const categories = new Set<InstructionCategory>();
    this.mentor.proficiencies.forEach(p => {
      if (p.isSelected) {
        categories.add(p.category as InstructionCategory);
      }
    });
    
    return Array.from(categories);
  }
  
  /**
   * Navigate back to mentor search
   */
  goBack(): void {
    this.router.navigate(['/mentors']);
  }
}
