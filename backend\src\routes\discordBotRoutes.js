const express = require('express');
const {
  getUserByDiscordId,
  updateUserProficiencies,
  getMentorsForBot,
  syncUserFromDiscord,
  getBotHealth,
  approveMentorRequest,
  denyMentorRequest
} = require('../controllers/discordBotController');

const router = express.Router();

// Health check for Discord bot
router.get('/health', getBotHealth);

// Get user by Discord ID
router.get('/user/:discordId', getUserByDiscordId);

// Get all mentors (formatted for Discord bot)
router.get('/mentors', getMentorsForBot);

// Sync user data from Discord (create/update user)
router.post('/sync-user', syncUserFromDiscord);

// Update user proficiencies
router.put('/user/:discordId/proficiencies', updateUserProficiencies);

// Approve mentor request (Discord bot only)
router.post('/approve-mentor', approveMentorRequest);

// Deny mentor request (Discord bot only)
router.post('/deny-mentor', denyMentorRequest);

module.exports = router;
