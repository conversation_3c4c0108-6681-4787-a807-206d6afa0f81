const { body, param, query, validationResult } = require('express-validator');
const { sanitizeBody } = require('express-validator');
const mongoSanitize = require('express-mongo-sanitize');

/**
 * Middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: formattedErrors
    });
  }
  
  next();
};

/**
 * Common validation rules
 */
const commonValidations = {
  // MongoDB ObjectId validation
  mongoId: param('id')
    .isMongoId()
    .withMessage('Invalid ID format'),

  // Discord ID validation
  discordId: body('discordId')
    .optional()
    .isString()
    .isLength({ min: 17, max: 19 })
    .matches(/^\d+$/)
    .withMessage('Discord ID must be a valid snowflake ID'),

  // Email validation
  email: body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Must be a valid email address'),

  // Username validation
  username: body('username')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 32 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username must be 2-32 characters and contain only letters, numbers, underscores, and hyphens'),

  // Display name validation
  displayName: body('displayName')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .escape()
    .withMessage('Display name must be 1-50 characters'),

  // Bio validation
  bio: body('bio')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .escape()
    .withMessage('Bio must not exceed 500 characters'),

  // Proficiency level validation
  proficiencyLevel: (field) => body(field)
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
    .withMessage('Proficiency level must be one of: beginner, intermediate, advanced, expert'),

  // Pagination validation
  page: query('page')
    .optional()
    .isInt({ min: 1 })
    .toInt()
    .withMessage('Page must be a positive integer'),

  limit: query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .toInt()
    .withMessage('Limit must be between 1 and 100'),

  // Search query validation
  searchQuery: query('q')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .escape()
    .withMessage('Search query must be 1-100 characters')
};

/**
 * Auth validation rules
 */
const authValidation = {
  discordCallback: [
    body('code')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 1, max: 1000 })
      .withMessage('Discord authorization code is required'),
    handleValidationErrors
  ]
};

/**
 * User validation rules
 */
const userValidation = {
  updateProfile: [
    commonValidations.displayName,
    commonValidations.bio,
    body('proficiencies')
      .optional()
      .isObject()
      .withMessage('Proficiencies must be an object'),
    body('proficiencies.streaming')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
      .withMessage('Invalid streaming proficiency level'),
    body('proficiencies.contentCreation')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
      .withMessage('Invalid content creation proficiency level'),
    body('proficiencies.communityBuilding')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
      .withMessage('Invalid community building proficiency level'),
    handleValidationErrors
  ],

  mentorApplication: [
    body('reason')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 50, max: 1000 })
      .escape()
      .withMessage('Reason must be 50-1000 characters'),
    body('experience')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 20, max: 1000 })
      .escape()
      .withMessage('Experience must be 20-1000 characters'),
    body('expertise')
      .isArray({ min: 1 })
      .withMessage('At least one area of expertise is required'),
    body('expertise.*')
      .isIn(['streaming', 'contentCreation', 'communityBuilding', 'marketing', 'technical'])
      .withMessage('Invalid expertise area'),
    body('availability')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 5, max: 200 })
      .escape()
      .withMessage('Availability must be 5-200 characters'),
    handleValidationErrors
  ],

  getUserById: [
    commonValidations.mongoId,
    handleValidationErrors
  ],

  searchUsers: [
    commonValidations.searchQuery,
    commonValidations.page,
    commonValidations.limit,
    query('proficiency')
      .optional()
      .isIn(['streaming', 'contentCreation', 'communityBuilding'])
      .withMessage('Invalid proficiency filter'),
    query('level')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
      .withMessage('Invalid proficiency level filter'),
    handleValidationErrors
  ]
};

/**
 * Content validation rules
 */
const contentValidation = {
  createContent: [
    body('title')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 5, max: 200 })
      .escape()
      .withMessage('Title must be 5-200 characters'),
    body('description')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 20, max: 2000 })
      .escape()
      .withMessage('Description must be 20-2000 characters'),
    body('content')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 50 })
      .withMessage('Content must be at least 50 characters'),
    body('category')
      .notEmpty()
      .isIn(['streaming', 'content-creation', 'community-building', 'marketing', 'technical'])
      .withMessage('Invalid category'),
    body('difficulty')
      .notEmpty()
      .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
      .withMessage('Invalid difficulty level'),
    body('tags')
      .optional()
      .isArray({ max: 10 })
      .withMessage('Maximum 10 tags allowed'),
    body('tags.*')
      .isString()
      .trim()
      .isLength({ min: 2, max: 30 })
      .matches(/^[a-zA-Z0-9-_]+$/)
      .withMessage('Tags must be 2-30 characters and contain only letters, numbers, hyphens, and underscores'),
    handleValidationErrors
  ],

  updateContent: [
    commonValidations.mongoId,
    body('title')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 5, max: 200 })
      .escape()
      .withMessage('Title must be 5-200 characters'),
    body('description')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 20, max: 2000 })
      .escape()
      .withMessage('Description must be 20-2000 characters'),
    body('content')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 50 })
      .withMessage('Content must be at least 50 characters'),
    body('category')
      .optional()
      .isIn(['streaming', 'content-creation', 'community-building', 'marketing', 'technical'])
      .withMessage('Invalid category'),
    body('difficulty')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced', 'expert'])
      .withMessage('Invalid difficulty level'),
    handleValidationErrors
  ],

  getContentById: [
    commonValidations.mongoId,
    handleValidationErrors
  ],

  addComment: [
    commonValidations.mongoId,
    body('content')
      .notEmpty()
      .isString()
      .trim()
      .isLength({ min: 1, max: 1000 })
      .escape()
      .withMessage('Comment must be 1-1000 characters'),
    handleValidationErrors
  ]
};

module.exports = {
  handleValidationErrors,
  commonValidations,
  authValidation,
  userValidation,
  contentValidation
};
