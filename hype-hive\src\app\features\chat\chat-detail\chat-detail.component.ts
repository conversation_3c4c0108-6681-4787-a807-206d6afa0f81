import { Compo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, ElementRef, AfterViewChecked } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, Validators } from '@angular/forms';
import { Subscription, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { ChatService } from '../../../core/services/chat.service';
import { SocketService } from '../../../core/services/socket.service';
import { AuthService } from '../../../core/services/auth.service';
import { MonitoringService } from '../../../core/services/monitoring.service';
import { Chat, ChatMessage, NewMessageEvent } from '../../../core/models/chat.model';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-chat-detail',
  templateUrl: './chat-detail.component.html',
  styleUrls: ['./chat-detail.component.scss'],
  standalone: false
})
export class ChatDetailComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('messageContainer') messageContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  chat: Chat | null = null;
  isLoading = true;
  errorMessage = '';
  messageContent = new FormControl('', [Validators.required]);
  currentUser: User | null = null;
  typingUsers: string[] = [];
  socketConnected = false;
  
  private chatId: string = '';
  private subscriptions: Subscription[] = [];
  private destroy$ = new Subject<void>();
  private typingTimeout: any;
  private shouldScrollToBottom = true;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private chatService: ChatService,
    private socketService: SocketService,
    private authService: AuthService,
    private monitoringService: MonitoringService
  ) { }

  ngOnInit(): void {
    // Get current user
    this.currentUser = this.authService.getCurrentUser();
    
    // Initialize socket connection
    this.socketService.initializeSocket();
    
    // Subscribe to socket connection status
    this.subscriptions.push(
      this.socketService.getConnectionStatus().subscribe(connected => {
        this.socketConnected = connected;
      })
    );
    
    // Get chat ID from route params
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.chatId = id;
        this.loadChat();
      } else {
        this.router.navigate(['/chat']);
      }
    });
    
    // Subscribe to typing users
    this.subscriptions.push(
      this.chatService.getTypingUsers(this.chatId).subscribe(users => {
        this.typingUsers = users;
      })
    );
    
    // Listen for typing events
    this.messageContent.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(value => {
      if (value && value.trim().length > 0) {
        this.sendTypingIndicator();
      } else {
        this.sendStopTypingIndicator();
      }
    });
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
    }
  }

  /**
   * Load chat details
   */
  loadChat(): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.subscriptions.push(
      this.chatService.getChatById(this.chatId).subscribe({
        next: (chat) => {
          this.chat = chat;
          this.isLoading = false;
          this.shouldScrollToBottom = true;
          
          // Mark messages as read
          this.markMessagesAsRead();
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to load chat';
          this.isLoading = false;
        }
      })
    );
  }

  /**
   * Mark all messages in the chat as read
   */
  markMessagesAsRead(): void {
    if (!this.chat) {
      return;
    }
    
    this.chatService.markMessagesAsRead(this.chatId).subscribe();
  }

  /**
   * Send a message
   */
  sendMessage(): void {
    if (!this.messageContent.valid || !this.chat) {
      return;
    }
    
    const content = this.messageContent.value?.trim();
    if (!content) {
      return;
    }
    
    // Clear input
    this.messageContent.reset();
    
    // Focus input field
    setTimeout(() => {
      this.messageInput.nativeElement.focus();
    });
    
    // Send stop typing indicator
    this.sendStopTypingIndicator();
    
    // Record start time for delivery time tracking
    const startTime = Date.now();
    
    // Send message
    this.chatService.sendMessage(this.chatId, content).subscribe({
      next: (message) => {
        // Calculate delivery time
        const deliveryTime = Date.now() - startTime;
        
        // Track delivery time
        this.monitoringService.trackEvent('message_delivery_time', { deliveryTime });
        
        // Ensure chat is updated with new message
        if (this.chat && !this.chat.messages.some(m => m._id === message._id)) {
          this.chat.messages.push(message);
          this.chat.lastMessage = {
            content: message.content,
            sender: message.sender,
            createdAt: message.createdAt
          };
          this.shouldScrollToBottom = true;
        }
      },
      error: (error) => {
        console.error('Failed to send message:', error);
      }
    });
  }

  /**
   * Send typing indicator
   */
  sendTypingIndicator(): void {
    if (!this.chat) {
      return;
    }
    
    // Clear previous timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
    
    // Send typing indicator
    this.chatService.sendTyping(this.chatId);
    
    // Set timeout to stop typing after 5 seconds of inactivity
    this.typingTimeout = setTimeout(() => {
      this.sendStopTypingIndicator();
    }, 5000);
  }

  /**
   * Send stop typing indicator
   */
  sendStopTypingIndicator(): void {
    if (!this.chat) {
      return;
    }
    
    // Clear timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
      this.typingTimeout = null;
    }
    
    // Send stop typing indicator
    this.chatService.sendStopTyping(this.chatId);
  }

  /**
   * Scroll to bottom of message container
   */
  scrollToBottom(): void {
    try {
      if (this.messageContainer) {
        this.messageContainer.nativeElement.scrollTop = 
          this.messageContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
    
    this.shouldScrollToBottom = false;
  }

  /**
   * Get chat display name
   * @returns Display name for the chat
   */
  getChatDisplayName(): string {
    if (!this.chat) {
      return 'Chat';
    }
    
    if (this.chat.isGroupChat && this.chat.groupName) {
      return this.chat.groupName;
    }
    
    // For direct messages, show the other participant's name
    if (this.currentUser && this.chat.participants) {
      const otherParticipant = this.chat.participants.find(
        p => p._id !== this.currentUser?._id
      );
      
      if (otherParticipant) {
        return otherParticipant.discordUsername;
      }
    }
    
    return 'Chat';
  }

  /**
   * Get chat avatar
   * @returns Avatar URL
   */
  getChatAvatar(): string {
    if (!this.chat) {
      return 'assets/images/default-avatar.png';
    }
    
    // For direct messages, show the other participant's avatar
    if (!this.chat.isGroupChat && this.currentUser && this.chat.participants) {
      const otherParticipant = this.chat.participants.find(
        p => p._id !== this.currentUser?._id
      );
      
      if (otherParticipant && otherParticipant.discordAvatar) {
        return otherParticipant.discordAvatar;
      }
    }
    
    // Default avatar for group chats or if no avatar is available
    return 'assets/images/default-avatar.png';
  }

  /**
   * Check if a message is from the current user
   * @param message Message to check
   * @returns True if the message is from the current user
   */
  isOwnMessage(message: ChatMessage): boolean {
    if (!this.currentUser) {
      return false;
    }
    
    const senderId = typeof message.sender === 'string' 
      ? message.sender 
      : message.sender._id;
      
    return senderId === this.currentUser._id;
  }

  /**
   * Get typing indicator text
   * @returns Typing indicator text
   */
  getTypingText(): string {
    if (this.typingUsers.length === 0) {
      return '';
    }
    
    if (this.typingUsers.length === 1) {
      return `${this.typingUsers[0]} is typing...`;
    }
    
    if (this.typingUsers.length === 2) {
      return `${this.typingUsers[0]} and ${this.typingUsers[1]} are typing...`;
    }
    
    return 'Several people are typing...';
  }

  /**
   * Go back to chat list
   */
  goBack(): void {
    this.router.navigate(['/chat']);
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    
    // Complete destroy subject
    this.destroy$.next();
    this.destroy$.complete();
    
    // Leave chat room
    this.chatService.leaveCurrentChat();
    
    // Clear typing timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }
}